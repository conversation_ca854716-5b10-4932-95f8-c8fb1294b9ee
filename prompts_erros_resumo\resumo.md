📊 Rate limit atualizado - User: user, Messages: +1, Tokens: +500
🚀 Processamento IMEDIATO para administracao
🤖 Processando mensagem para administracao: me de detalhes do PROTOCOLO 20250060168...
🤖 [TS] Processando mensagem para administracao - VERSÃO TYPESCRIPT
🔍 [TS] Arquivo: deepSeekService.ts sendo executado
🔍 [DEBUG] Mensagem original: me de detalhes do PROTOCOLO 20250060168
🔍 [DEBUG] Mensagem lowercase: me de detalhes do protocolo 20250060168
🔍 Buscando dados completos para transparência total...
✅ Dados completos carregados para transparência
🔍 Buscando documentos relevantes via RAG...
🔍 Busca híbrida: "me de detalhes do PROTOCOLO 20250060168" | Secretaria: administracao
🔍 Buscando: "me de detalhes do PROTOCOLO 20250060168" (top-6, threshold: 0.5)
✅ Encontrados 6 documentos relevantes
✅ Busca híbrida: 3 documentos finais
✅ RAG: 3 documentos encontrados em 665ms
🔍 Debug tokens: {
  raw_usage: {
    prompt_tokens: 5774,
    completion_tokens: 389,
    total_tokens: 6163,
    prompt_tokens_details: { cached_tokens: 5504 },
    prompt_cache_hit_tokens: 5504,
    prompt_cache_miss_tokens: 270
  },
  calculated: { prompt: 5774, completion: 389, total: 6163 }
}
💰 Debug custos: {
  isDiscount: true,
  tokens: { prompt: 5774, completion: 389, total: 6163 },
  costs: { promptCost: 0.00077949, completionCost: 0.00021395000000000002 },
  totalCost: 0.00099344,
  withDiscount: 0.00099344,
  savings: 0.00099344
}
✅ Resposta gerada em 23601ms
📊 Tokens: 6163 | Custo: $0.0010 | Com desconto: $0.0010
💰 Custo registrado: $0.0010 | Tokens: 6163 | Cache: MISS
Erro ao garantir conversa: error: invalid input syntax for type bigint: "NaN"
    at D:\PROJETOS-BACKUP\prefeitura_virtual_ia\node_modules\pg\lib\client.js:545:17
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async PostgreSQLRepository.findUserById (D:\PROJETOS-BACKUP\prefeitura_virtual_ia\backend\src\repositories\PostgreSQLRepository.ts:261:22)
    at async Function.ensureConversation (D:\PROJETOS-BACKUP\prefeitura_virtual_ia\backend\src\controllers\conversationController.ts:47:20)
    at async processMessage (D:\PROJETOS-BACKUP\prefeitura_virtual_ia\backend\src\controllers\chatController.ts:78:34) {
  length: 145,
  severity: 'ERROR',
  code: '22P02',
  detail: undefined,
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: "unnamed portal parameter $1 = '...'",
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'numutils.c',
  line: '879',
  routine: 'pg_strtoint64_safe'
}
Erro ao criar/buscar conversa: error: invalid input syntax for type bigint: "NaN"
    at D:\PROJETOS-BACKUP\prefeitura_virtual_ia\node_modules\pg\lib\client.js:545:17
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async PostgreSQLRepository.findUserById (D:\PROJETOS-BACKUP\prefeitura_virtual_ia\backend\src\repositories\PostgreSQLRepository.ts:261:22)
    at async Function.ensureConversation (D:\PROJETOS-BACKUP\prefeitura_virtual_ia\backend\src\controllers\conversationController.ts:47:20)
    at async processMessage (D:\PROJETOS-BACKUP\prefeitura_virtual_ia\backend\src\controllers\chatController.ts:78:34) {
  length: 145,
  severity: 'ERROR',
  code: '22P02',
  detail: undefined,
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: "unnamed portal parameter $1 = '...'",
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'numutils.c',
  line: '879',
  routine: 'pg_strtoint64_safe'
}
📊 Rate limit atualizado - User: user, Messages: +1, Tokens: +500
🚀 Processamento IMEDIATO para administracao
🤖 Processando mensagem para administracao: Qual o nome dos socios do cnpj: 44.067.277/0001-07...
🤖 [TS] Processando mensagem para administracao - VERSÃO TYPESCRIPT
🔍 [TS] Arquivo: deepSeekService.ts sendo executado
🔍 [DEBUG] Mensagem original: Qual o nome dos socios do cnpj: 44.067.277/0001-07?
🔍 [DEBUG] Mensagem lowercase: qual o nome dos socios do cnpj: 44.067.277/0001-07?
🔍 Buscando dados completos para transparência total...
✅ Dados completos carregados para transparência
🔍 Buscando documentos relevantes via RAG...
🔍 Busca híbrida: "Qual o nome dos socios do cnpj: 44.067.277/0001-07?" | Secretaria: administracao
🔍 Buscando: "Qual o nome dos socios do cnpj: 44.067.277/0001-07?" (top-6, threshold: 0.5)
✅ Encontrados 0 documentos relevantes
✅ Busca híbrida: 3 documentos finais
✅ RAG: 3 documentos encontrados em 1155ms
🔍 Debug tokens: {
  raw_usage: {
    prompt_tokens: 5792,
    completion_tokens: 246,
    total_tokens: 6038,
    prompt_tokens_details: { cached_tokens: 5568 },
    prompt_cache_hit_tokens: 5568,
    prompt_cache_miss_tokens: 224
  },
  calculated: { prompt: 5792, completion: 246, total: 6038 }
}
💰 Debug custos: {
  isDiscount: true,
  tokens: { prompt: 5792, completion: 246, total: 6038 },
  costs: { promptCost: 0.0007819200000000001, completionCost: 0.0001353 },
  totalCost: 0.0009172200000000001,
  withDiscount: 0.0009172200000000001,
  savings: 0.0009172200000000001
}
✅ Resposta gerada em 17730ms
📊 Tokens: 6038 | Custo: $0.0009 | Com desconto: $0.0009
💰 Custo registrado: $0.0009 | Tokens: 6038 | Cache: MISS
Erro ao garantir conversa: error: invalid input syntax for type bigint: "NaN"
    at D:\PROJETOS-BACKUP\prefeitura_virtual_ia\node_modules\pg\lib\client.js:545:17
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async PostgreSQLRepository.findUserById (D:\PROJETOS-BACKUP\prefeitura_virtual_ia\backend\src\repositories\PostgreSQLRepository.ts:261:22)
    at async Function.ensureConversation (D:\PROJETOS-BACKUP\prefeitura_virtual_ia\backend\src\controllers\conversationController.ts:47:20)
    at async processMessage (D:\PROJETOS-BACKUP\prefeitura_virtual_ia\backend\src\controllers\chatController.ts:78:34) {
  length: 145,
  severity: 'ERROR',
  code: '22P02',
  detail: undefined,
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: "unnamed portal parameter $1 = '...'",
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'numutils.c',
  line: '879',
  routine: 'pg_strtoint64_safe'
}
Erro ao criar/buscar conversa: error: invalid input syntax for type bigint: "NaN"
    at D:\PROJETOS-BACKUP\prefeitura_virtual_ia\node_modules\pg\lib\client.js:545:17
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async PostgreSQLRepository.findUserById (D:\PROJETOS-BACKUP\prefeitura_virtual_ia\backend\src\repositories\PostgreSQLRepository.ts:261:22)
    at async Function.ensureConversation (D:\PROJETOS-BACKUP\prefeitura_virtual_ia\backend\src\controllers\conversationController.ts:47:20)
    at async processMessage (D:\PROJETOS-BACKUP\prefeitura_virtual_ia\backend\src\controllers\chatController.ts:78:34) {
  length: 145,
  severity: 'ERROR',
  code: '22P02',
  detail: undefined,
  hint: undefined,
  position: undefined,
  internalPosition: undefined,
  internalQuery: undefined,
  where: "unnamed portal parameter $1 = '...'",
  schema: undefined,
  table: undefined,
  column: undefined,
  dataType: undefined,
  constraint: undefined,
  file: 'numutils.c',
  line: '879',
  routine: 'pg_strtoint64_safe'
}
📊 Rate limit atualizado - User: user, Messages: +1, Tokens: +500
