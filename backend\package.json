{"name": "chatbot-backend", "version": "1.0.0", "description": "Backend API para o Sistema de Chatbot Inteligente", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:create-conversations": "tsx src/scripts/execute-conversation-tables.ts"}, "dependencies": {"@prisma/client": "^5.7.1", "@types/pg": "^8.15.4", "axios": "^1.11.0", "bcrypt": "^5.1.1", "bull": "^4.12.2", "chalk": "^5.4.1", "chromadb": "^1.8.1", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.4", "openai": "^4.26.0", "pg": "^8.16.3", "redis": "^4.6.12", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/bull": "^4.10.0", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.5", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "prisma": "^5.7.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}