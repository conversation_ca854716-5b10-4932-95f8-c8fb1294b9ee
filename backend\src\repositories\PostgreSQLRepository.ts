/**
 * Repository para PostgreSQL usando estrutura real do banco
 * Baseado nas tabelas: usuarios, departamentos, user_departamentos
 */

import { Client, Pool } from 'pg';
import bcrypt from 'bcrypt';

interface Usuario {
  id: number;
  nome: string;
  email: string;
  cpf: string;
  senha: string;
  conta_ativa: boolean;
  servidor: boolean;
  created_at: Date;
  updated_at: Date;
}

interface Departamento {
  id: number;
  descricao: string;
  endereco?: string;
  email?: string;
  ativo: boolean;
  horario_atendimento?: string;
  created_at: Date;
  updated_at: Date;
}

interface UserDepartamento {
  id: number;
  id_user: number;
  id_departamento: number;
}

export class PostgreSQLRepository {
  private pool: Pool;

  constructor() {
    this.pool = new Pool({
      host: '*************',
      port: 5411,
      user: 'otto',
      password: 'otto',
      database: 'pv_valparaiso',
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 10000,
    });
  }

  /**
   * Buscar usuário por email ou CPF
   */
  async findUserByEmailOrCPF(login: string): Promise<Usuario | null> {
    const client = await this.pool.connect();
    
    try {
      // Verificar se é email (contém @) ou CPF
      const isEmail = login.includes('@');
      const query = isEmail 
        ? 'SELECT * FROM usuarios WHERE email = $1 AND conta_ativa = true'
        : 'SELECT * FROM usuarios WHERE cpf = $1 AND conta_ativa = true';
      
      const result = await client.query(query, [login]);
      
      if (result.rows.length === 0) {
        return null;
      }
      
      return result.rows[0] as Usuario;
    } finally {
      client.release();
    }
  }

  /**
   * Buscar departamentos do usuário
   */
  async getUserDepartments(userId: number): Promise<Departamento[]> {
    const client = await this.pool.connect();
    
    try {
      const query = `
        SELECT d.*
        FROM departamentos d
        INNER JOIN user_departamentos ud ON d.id = ud.id_departamento
        WHERE ud.id_user = $1 AND d.ativo = true
      `;
      
      const result = await client.query(query, [userId]);
      return result.rows as Departamento[];
    } finally {
      client.release();
    }
  }

  /**
   * Buscar departamento por ID
   */
  async getDepartmentById(departmentId: number): Promise<Departamento | null> {
    const client = await this.pool.connect();
    
    try {
      const query = 'SELECT * FROM departamentos WHERE id = $1 AND ativo = true';
      const result = await client.query(query, [departmentId]);
      
      if (result.rows.length === 0) {
        return null;
      }
      
      return result.rows[0] as Departamento;
    } finally {
      client.release();
    }
  }

  /**
   * Buscar departamento por descrição (nome)
   */
  async getDepartmentByName(name: string): Promise<Departamento | null> {
    const client = await this.pool.connect();
    
    try {
      const query = `
        SELECT * FROM departamentos 
        WHERE LOWER(descricao) LIKE LOWER($1) AND ativo = true
        LIMIT 1
      `;
      const result = await client.query(query, [`%${name}%`]);
      
      if (result.rows.length === 0) {
        return null;
      }
      
      return result.rows[0] as Departamento;
    } finally {
      client.release();
    }
  }

  /**
   * Listar todos os departamentos ativos
   */
  async getAllDepartments(): Promise<Departamento[]> {
    const client = await this.pool.connect();
    
    try {
      const query = 'SELECT * FROM departamentos WHERE ativo = true ORDER BY descricao';
      const result = await client.query(query);
      return result.rows as Departamento[];
    } finally {
      client.release();
    }
  }

  /**
   * Validar senha do usuário usando bcrypt
   */
  async validatePassword(user: Usuario, password: string): Promise<boolean> {
    try {
      if (!user.senha) {
        return false;
      }
      
      // Validar com bcrypt (suporta tanto $2y$ quanto $2b$)
      const isValid = await bcrypt.compare(password, user.senha);
      return isValid;
    } catch (error) {
      console.error('Erro ao validar senha:', error);
      return false;
    }
  }

  /**
   * Atualizar último login do usuário
   */
  async updateLastLogin(userId: number): Promise<void> {
    const client = await this.pool.connect();
    
    try {
      const query = 'UPDATE usuarios SET updated_at = NOW() WHERE id = $1';
      await client.query(query, [userId]);
    } finally {
      client.release();
    }
  }

  /**
   * Verificar se usuário tem acesso ao departamento
   */
  async userHasAccessToDepartment(userId: number, departmentId: number): Promise<boolean> {
    const client = await this.pool.connect();
    
    try {
      const query = `
        SELECT COUNT(*) as count
        FROM user_departamentos 
        WHERE id_user = $1 AND id_departamento = $2
      `;
      
      const result = await client.query(query, [userId, departmentId]);
      return parseInt(result.rows[0].count) > 0;
    } finally {
      client.release();
    }
  }

  /**
   * Buscar estatísticas gerais
   */
  async getStats() {
    const client = await this.pool.connect();
    
    try {
      const userCountResult = await client.query('SELECT COUNT(*) as total FROM usuarios WHERE conta_ativa = true');
      const departmentCountResult = await client.query('SELECT COUNT(*) as total FROM departamentos WHERE ativo = true');
      
      return {
        totalUsers: parseInt(userCountResult.rows[0].total),
        totalDepartments: parseInt(departmentCountResult.rows[0].total)
      };
    } finally {
      client.release();
    }
  }

  /**
   * Fechar conexões
   */
  async close(): Promise<void> {
    await this.pool.end();
  }

  /**
   * Testar conexão
   */
  async testConnection(): Promise<boolean> {
    const client = await this.pool.connect();
    
    try {
      await client.query('SELECT 1');
      return true;
    } catch (error) {
      console.error('Erro ao testar conexão PostgreSQL:', error);
      return false;
    } finally {
      client.release();
    }
  }

  /**
   * Buscar usuário por ID
   */
  async findUserById(userId: string): Promise<Usuario | null> {
    const client = await this.pool.connect();
    try {
      const query = 'SELECT * FROM usuarios WHERE id = $1';
      const result = await client.query(query, [parseInt(userId)]);
      return result.rows[0] || null;
    } finally {
      client.release();
    }
  }

  /**
   * Buscar ou criar conversa ativa
   */
  async getOrCreateActiveConversation(userId: number): Promise<any> {
    const client = await this.pool.connect();
    try {
      // Primeiro tenta buscar uma conversa ativa
      let query = `
        SELECT * FROM conversations 
        WHERE user_id = $1 AND status = 'active'
        ORDER BY updated_at DESC
        LIMIT 1
      `;
      
      let result = await client.query(query, [userId]);
      
      if (result.rows.length > 0) {
        return result.rows[0];
      }
      
      // Se não encontrar, cria uma nova com UUID
      query = `
        INSERT INTO conversations (id, user_id, title, status, created_at, updated_at, message_count)
        VALUES (gen_random_uuid(), $1, $2, 'active', NOW(), NOW(), 0)
        RETURNING *
      `;
      
      result = await client.query(query, [userId, 'Nova Conversa']);
      return result.rows[0];
    } catch (error: any) {
      console.error('Erro ao criar conversa:', error);
      throw error;
    } finally {
      client.release();
    }
  }

  // ====== MÉTODOS PARA CONVERSAS ======

  /**
   * Criar nova conversa
   */
  async createConversation(userId: number, title: string): Promise<any> {
    const query = `
      INSERT INTO conversations (user_id, title)
      VALUES ($1, $2)
      RETURNING *
    `;
    
    const result = await this.pool.query(query, [userId, title]);
    return result.rows[0];
  }

  /**
   * Buscar conversas do usuário
   */
  async getUserConversations(userId: number, limit: number = 50): Promise<any[]> {
    const query = `
      SELECT id, title, last_message, message_count, created_at, updated_at
      FROM conversations
      WHERE user_id = $1 AND status = 'active'
      ORDER BY updated_at DESC
      LIMIT $2
    `;
    
    const result = await this.pool.query(query, [userId, limit]);
    return result.rows;
  }

  /**
   * Buscar conversa por ID
   */
  async getConversationById(conversationId: string, userId: number): Promise<any> {
    const query = `
      SELECT * FROM conversations
      WHERE id = $1 AND user_id = $2
    `;
    
    const result = await this.pool.query(query, [conversationId, userId]);
    return result.rows[0];
  }

  /**
   * Deletar conversa (soft delete)
   */
  async deleteConversation(conversationId: string, userId: number): Promise<boolean> {
    const query = `
      UPDATE conversations
      SET status = 'deleted', updated_at = CURRENT_TIMESTAMP
      WHERE id = $1 AND user_id = $2
      RETURNING id
    `;
    
    const result = await this.pool.query(query, [conversationId, userId]);
    return result.rowCount > 0;
  }

  /**
   * Adicionar mensagem à conversa
   */
  async addMessage(params: {
    conversationId: string;
    content: string;
    role: 'user' | 'assistant' | 'system';
    tokensUsed?: number;
    cost?: number;
    cacheHit?: boolean;
    cacheType?: string;
    processingTime?: number;
    metadata?: any;
  }): Promise<any> {
    const query = `
      INSERT INTO messages (
        conversation_id, content, role, tokens_used, cost, 
        cache_hit, cache_type, processing_time, metadata
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
      RETURNING *
    `;
    
    const values = [
      params.conversationId,
      params.content,
      params.role,
      params.tokensUsed || null,
      params.cost || null,
      params.cacheHit || false,
      params.cacheType || null,
      params.processingTime || null,
      params.metadata ? JSON.stringify(params.metadata) : null
    ];
    
    const result = await this.pool.query(query, values);
    return result.rows[0];
  }

  /**
   * Buscar mensagens de uma conversa
   */
  async getConversationMessages(conversationId: string, limit: number = 100): Promise<any[]> {
    const query = `
      SELECT * FROM messages
      WHERE conversation_id = $1
      ORDER BY created_at ASC
      LIMIT $2
    `;
    
    const result = await this.pool.query(query, [conversationId, limit]);
    return result.rows;
  }

  /**
   * Atualizar título da conversa
   */
  async updateConversationTitle(conversationId: string, title: string, userId: number): Promise<boolean> {
    const query = `
      UPDATE conversations
      SET title = $1, updated_at = CURRENT_TIMESTAMP
      WHERE id = $2 AND user_id = $3
      RETURNING id
    `;
    
    const result = await this.pool.query(query, [title, conversationId, userId]);
    return result.rowCount > 0;
  }

  /**
   * Buscar ou criar conversa ativa para o usuário
   */
  async getOrCreateActiveConversation(userId: number): Promise<any> {
    // Primeiro tenta buscar uma conversa recente sem mensagens
    const findQuery = `
      SELECT * FROM conversations
      WHERE user_id = $1 
        AND status = 'active' 
        AND message_count = 0
        AND created_at > NOW() - INTERVAL '1 hour'
      ORDER BY created_at DESC
      LIMIT 1
    `;
    
    let result = await this.pool.query(findQuery, [userId]);
    
    if (result.rows.length > 0) {
      return result.rows[0];
    }
    
    // Se não encontrar, cria uma nova
    return this.createConversation(userId, 'Nova Conversa');
  }
}

// Singleton instance
export const postgresRepository = new PostgreSQLRepository();